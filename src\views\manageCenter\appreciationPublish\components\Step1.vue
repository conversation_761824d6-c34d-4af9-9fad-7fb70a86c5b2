<template>
  <div class="step1">
    <a-spin :spinning="entrustDetailLoading" tip="正在加载委托详情...">
      <a-form :model="formData" :rules="rules" ref="formRef" :scroll-to-first-error="true">
        <!-- 服务类型板块 -->
        <div class="form-section">
          <h3 class="section-title">服务类型</h3>
          <div class="form-row service-type-row">
            <a-form-item label="关联委托单号" name="entrustOrderId" required class="entrust-order-item">
              <a-select
                v-model:value="formData.entrustOrderId"
                placeholder="请选择关联委托单号"
                size="large"
                class="entrust-order-select"
                :loading="entrustOrderLoading || entrustDetailLoading"
                show-search
                :filter-option="filterOption"
                :disabled="entrustDetailLoading"
              >
                <a-select-option v-for="item in entrustOrderList" :key="item" :value="item">
                  {{ item }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="服务类型" name="serviceType" required class="service-type-item">
              <a-select
                v-model:value="formData.serviceType"
                @change="handleServiceTypeChange"
                placeholder="请选择服务类型"
                size="large"
                class="service-type-select"
                :disabled="props.isEditMode"
              >
                <a-select-option :value="2">发布资产处置</a-select-option>
                <a-select-option :value="3">发布采购信息</a-select-option>
              </a-select>
              <div v-if="props.isEditMode" class="edit-mode-tip">
                <span class="tip-text">编辑模式下不允许修改服务类型</span>
              </div>
            </a-form-item>
            <!-- 占位元素，保持三等分布局 -->
            <div class="entrust-placeholder"></div>
          </div>

          <!-- 采购信息特有字段 -->
          <div v-if="formData.serviceType === 3" class="form-row">
            <a-form-item label="公告名称" :name="['entrustInfo', 'noticeName']" required class="full-width-item">
              <a-input v-model:value="formData.entrustInfo.noticeName" placeholder="请输入公告名称" size="large" />
            </a-form-item>
          </div>
        </div>

        <!-- 基本信息板块（仅发布资产处置显示） -->
        <div v-if="formData.serviceType === 2" class="form-section">
          <div class="section-title">基本信息</div>
          <div class="section-content">
            <!-- 第一行：处置单位、资产名称、资产编号 -->
            <div class="form-row asset-row">
              <a-form-item label="处置单位" :name="['entrustInfo', 'title']" required class="asset-item">
                <a-input v-model:value="formData.entrustInfo.title" placeholder="处置单位" size="large" disabled readonly />
              </a-form-item>
              <a-form-item label="资产名称" :name="['basicInfo', 'assetName']" required class="asset-item">
                <a-input v-model:value="formData.basicInfo.assetName" placeholder="请输入资产名称" size="large" />
              </a-form-item>
              <a-form-item label="资产编号" :name="['basicInfo', 'assetNo']" class="asset-item">
                <a-input v-model:value="formData.basicInfo.assetNo" placeholder="资产编号由系统生成" size="large" disabled readonly />
              </a-form-item>
            </div>

            <!-- 第二行：资产类型、资产数量、计量单位 -->
            <div class="form-row asset-row">
              <a-form-item label="资产类型" :name="['basicInfo', 'assetType']" required class="asset-item">
                <a-select v-model:value="formData.basicInfo.assetType" placeholder="请选择资产类型" size="large">
                  <!-- 这里需要通过接口获取数据，暂时使用示例数据 -->
                  <a-select-option :value="1">设备类</a-select-option>
                  <a-select-option :value="2">车辆类</a-select-option>
                  <a-select-option :value="3">房产类</a-select-option>
                  <a-select-option :value="4">其他</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="资产数量" :name="['basicInfo', 'quantity']" required class="asset-item">
                <a-input v-model:value="formData.basicInfo.quantity" placeholder="请输入资产数量" size="large" />
              </a-form-item>
              <a-form-item label="计量单位" :name="['basicInfo', 'unit']" required class="asset-item">
                <a-select v-model:value="formData.basicInfo.unit" placeholder="请选择计量单位" size="large">
                  <a-select-option value="台">台</a-select-option>
                  <a-select-option value="辆">辆</a-select-option>
                  <a-select-option value="套">套</a-select-option>
                  <a-select-option value="个">个</a-select-option>
                  <a-select-option value="件">件</a-select-option>
                  <a-select-option value="批">批</a-select-option>
                  <a-select-option value="米">米</a-select-option>
                </a-select>
              </a-form-item>
            </div>
            <div class="form-row asset-row">
              <a-form-item label="是否展示实际数量" :name="['basicInfo', 'quantityFlag']" required class="asset-detail-item">
                <a-radio-group v-model:value="formData.basicInfo.quantityFlag">
                  <a-radio :value="1">是</a-radio>
                  <a-radio :value="0">否</a-radio>
                </a-radio-group>
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 资产详情板块（仅资产处置显示） -->
        <div v-if="formData.serviceType === 2" class="form-section">
          <div class="section-title">资产详情</div>
          <div class="section-content">
            <!-- 第一行：使用年限、新旧程度、当前状态 -->
            <div class="form-row asset-detail-row">
              <a-form-item label="使用年限" :name="['basicInfo', 'serviceLife']" required class="asset-detail-item">
                <a-input-number
                  v-model:value="formData.basicInfo.serviceLife"
                  placeholder="请输入使用年限"
                  size="large"
                  style="width: 100%"
                  :min="0"
                  addon-after="年"
                />
              </a-form-item>
              <a-form-item label="新旧程度" :name="['basicInfo', 'depreciationDegree']" required class="asset-detail-item">
                <a-select v-model:value="formData.basicInfo.depreciationDegree" placeholder="请选择新旧程度" size="large">
                  <a-select-option :value="1">九成新</a-select-option>
                  <a-select-option :value="2">八成新</a-select-option>
                  <a-select-option :value="3">七成新</a-select-option>
                  <a-select-option :value="4">六成新</a-select-option>
                  <a-select-option :value="5">五成新</a-select-option>
                  <a-select-option :value="6">四成新</a-select-option>
                  <a-select-option :value="7">三成新</a-select-option>
                  <a-select-option :value="8">二成新</a-select-option>
                  <a-select-option :value="9">一成新</a-select-option>
                  <a-select-option :value="10">报废</a-select-option>
                </a-select>
              </a-form-item>
              <a-form-item label="当前状态" :name="['basicInfo', 'currentStatus']" required class="asset-detail-item">
                <a-radio-group v-model:value="formData.basicInfo.currentStatus">
                  <a-radio :value="1">在用</a-radio>
                  <a-radio :value="2">闲置</a-radio>
                  <a-radio :value="3">报废</a-radio>
                </a-radio-group>
              </a-form-item>
            </div>

            <!-- 第二行：评估价值、处置底价、处置时间 -->
            <div class="form-row asset-detail-row">
              <a-form-item label="评估价值" :name="['basicInfo', 'appraisalValue']" required class="asset-detail-item">
                <a-input-number
                  v-model:value="formData.basicInfo.appraisalValue"
                  placeholder="请输入评估价值"
                  size="large"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  addon-after="元"
                />
              </a-form-item>
              <a-form-item label="处置底价" :name="['basicInfo', 'disposalPrice']" required class="asset-detail-item">
                <a-input-number
                  v-model:value="formData.basicInfo.disposalPrice"
                  placeholder="请输入处置底价"
                  size="large"
                  style="width: 100%"
                  :min="0"
                  :precision="2"
                  addon-after="元"
                />
              </a-form-item>
              <a-form-item label="处置时间" :name="['basicInfo', 'disposalStartTime']" required class="asset-detail-item">
                <a-range-picker
                  v-model:value="disposalTimeRange"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="['开始时间', '结束时间']"
                  size="large"
                  style="width: 100%"
                  @change="handleDisposalTimeChange"
                />
              </a-form-item>
            </div>

            <!-- 第三行：付款方式、是否含税、税点 -->
            <div class="form-row asset-detail-row">
              <a-form-item label="付款方式" :name="['basicInfo', 'paymentMethod']" required class="asset-detail-item">
                <a-radio-group v-model:value="formData.basicInfo.paymentMethod">
                  <a-radio :value="1">全款</a-radio>
                  <a-radio :value="2">分期</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="是否含税" :name="['basicInfo', 'isTaxIncluded']" required class="asset-detail-item">
                <a-radio-group v-model:value="formData.basicInfo.isTaxIncluded">
                  <a-radio value="0">否</a-radio>
                  <a-radio value="1">是</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item
                label="税点"
                :name="['basicInfo', 'taxRate']"
                :required="formData.basicInfo.isTaxIncluded !== '0'"
                class="asset-detail-item"
              >
                <a-input-number
                  v-model:value="formData.basicInfo.taxRate"
                  placeholder="请输入税点"
                  size="large"
                  style="width: 100%"
                  :min="0"
                  :max="100"
                  :precision="2"
                  addon-after="%"
                  :disabled="formData.basicInfo.isTaxIncluded === '0'"
                />
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 存放位置板块（发布竞价标的时不显示） -->
        <div v-if="formData.serviceType !== 1" class="form-section">
          <h3 class="section-title">{{ formData.serviceType === 3 ? '所属地区' : '存放位置' }}</h3>
          <!-- 发布采购信息时只显示省市区选择 -->
          <div v-if="formData.serviceType === 3" class="form-row">
            <div class="location-selects">
              <a-form-item :name="['location', 'province']" class="location-area-item">
                <JAreaSelect
                  v-model:province="formData.location.province"
                  v-model:city="formData.location.city"
                  v-model:area="formData.location.area"
                  placeholder="请选择所属地区"
                  :level="3"
                />
              </a-form-item>
            </div>
          </div>
          <!-- 其他服务类型显示省市区选择和详细地址 -->
          <div v-else class="form-row location-row">
            <!-- 省市区级联选择区域（占一半空间） -->
            <div class="location-selects">
              <a-form-item :name="['location', 'province']" class="location-area-item">
                <JAreaSelect
                  v-model:province="formData.location.province"
                  v-model:city="formData.location.city"
                  v-model:area="formData.location.area"
                  placeholder="请选择存放位置"
                  :level="3"
                />
              </a-form-item>
            </div>

            <!-- 详细地址区域（占一半空间） -->
            <div class="detail-address">
              <a-form-item label="详细地址" :name="['location', 'detailAddress']" required class="detail-address-item">
                <a-input v-model:value="formData.location.detailAddress" placeholder="请输入详细地址" size="large">
                  <template #suffix>
                    <a-button type="text" @click="getCurrentLocation" :loading="props.locationLoading" class="location-btn" size="small">
                      <template #icon>
                        <EnvironmentOutlined />
                      </template>
                    </a-button>
                  </template>
                </a-input>
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 资料上传板块 -->
        <div class="form-section" v-if="formData.serviceType !== 1">
          <h3 class="section-title">资料上传</h3>

          <!-- 发布采购信息时只显示采购附件上传 -->
          <div v-if="formData.serviceType === 3">
            <div class="form-row">
              <a-form-item label="采购附件" :name="['materials', 'attachments']" required class="upload-item">
                <div class="upload-container">
                  <JUpload
                    v-model:value="formData.materials.attachments"
                    :multiple="true"
                    :max-count="5"
                    accept=".pdf,.doc,.docx,.xls,.xlsx"
                    :return-url="false"
                    class="upload-component upload-normal"
                  />
                  <div class="upload-tip">支持PDF、DOC、DOCX、XLS、XLSX格式</div>
                </div>
              </a-form-item>
            </div>
          </div>

          <!-- 发布资产处置时显示完整的资料上传 -->
          <div v-else-if="formData.serviceType === 2">
            <!-- 标的图片上传 -->
            <div class="form-row">
              <a-form-item label="标的图片" :name="['materials', 'images']" class="upload-item">
                <div class="upload-container">
                  <JUpload
                    v-model:value="formData.materials.images"
                    :multiple="true"
                    :max-count="10"
                    accept="image/*"
                    list-type="picture-card"
                    file-type="image"
                    :return-url="false"
                    class="upload-component upload-normal"
                  />
                  <div class="upload-tip">最多可上传10张图片，支持JPG、PNG格式，单个文件不超过5MB</div>
                </div>
              </a-form-item>
            </div>

            <!-- 附件上传 -->
            <div class="form-row">
              <a-form-item label="附件上传" :name="['materials', 'attachments']" class="upload-item">
                <div class="upload-container">
                  <JUpload
                    v-model:value="formData.materials.attachments"
                    :multiple="true"
                    :max-count="5"
                    accept=".pdf,.doc,.docx,.xls,.xlsx"
                    :return-url="false"
                    class="upload-component upload-normal"
                  />
                  <div class="upload-tip">支持PDF、DOC、DOCX、XLS、XLSX格式</div>
                </div>
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 特殊说明板块（仅资产处置显示） -->
        <div v-if="formData.serviceType === 2" class="form-section">
          <h3 class="section-title">特殊说明</h3>
          <div class="form-row">
            <a-form-item label="特殊说明" :name="['materials', 'specialNote']" class="upload-item">
              <a-textarea v-model:value="formData.materials.specialNote" placeholder="请输入特殊说明（如有）" :rows="3" size="large" />
            </a-form-item>
          </div>
        </div>
      </a-form>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
  import { watch, computed, ref, inject, toRef } from 'vue';
  import { EnvironmentOutlined } from '@ant-design/icons-vue';
  import JAreaSelect from '@/components/Form/src/jeecg/components/JAreaSelect.vue';
  import JUpload from '@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import type { PassedReviewItem } from '@/api/manageCenter/appreciationPublish';
  import dayjs, { Dayjs } from 'dayjs';

  // Props 定义
  interface Props {
    modelValue: any;
    locationLoading?: boolean;
    isEditMode?: boolean;
  }

  // Emits 定义
  interface Emits {
    (e: 'update:modelValue', value: any): void;
    (e: 'area-change', value: any): void;
    (e: 'get-current-location'): void;
    (e: 'service-type-change', value: number): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    locationLoading: false,
    isEditMode: false,
  });

  const emit = defineEmits<Emits>();

  // 注入委托单列表数据
  const entrustOrderList = inject<PassedReviewItem[]>('entrustOrderList', []);
  const entrustOrderLoading = inject<boolean>('entrustOrderLoading', false);
  const entrustDetailLoading = inject<boolean>('entrustDetailLoading', false);

  // 表单数据 - 使用toRef来保持响应式连接
  const formData = toRef(props, 'modelValue');

  // 处置时间范围
  const disposalTimeRange = ref<[Dayjs, Dayjs] | null>(null);

  // 初始化默认值
  const initDefaultValues = () => {
    // 设置处置单位默认值
    if (!formData.value.entrustInfo.title) {
      formData.value.entrustInfo.title = '灰谷科技有限公司';
    }
    if (!formData.value.entrustInfo.titleValue) {
      formData.value.entrustInfo.titleValue = '1003';
    }
  };

  // 初始化处置时间范围
  const initDisposalTimeRange = () => {
    if (formData.value.basicInfo.disposalStartTime && formData.value.basicInfo.disposalEndTime) {
      disposalTimeRange.value = [dayjs(formData.value.basicInfo.disposalStartTime), dayjs(formData.value.basicInfo.disposalEndTime)];
    } else {
      disposalTimeRange.value = null;
    }
  };

  // 监听处置时间相关字段变化，更新时间范围选择器
  watch(
    () => [formData.value.basicInfo.disposalStartTime, formData.value.basicInfo.disposalEndTime],
    () => {
      initDisposalTimeRange();
    },
    { immediate: true }
  );

  // 初始化默认值
  watch(
    () => formData.value,
    () => {
      initDefaultValues();
    },
    { immediate: true, deep: true }
  );

  // 监听服务类型变化，用于调试
  watch(
    () => formData.value.serviceType,
    (newVal, oldVal) => {
      console.log('Step1组件中serviceType变化:', { oldVal, newVal });
    }
  );

  // 处理处置时间变化
  const handleDisposalTimeChange = (dates: [Dayjs, Dayjs] | null) => {
    if (dates) {
      formData.value.basicInfo.disposalStartTime = dates[0].format('YYYY-MM-DD HH:mm:ss');
      formData.value.basicInfo.disposalEndTime = dates[1].format('YYYY-MM-DD HH:mm:ss');
    } else {
      formData.value.basicInfo.disposalStartTime = '';
      formData.value.basicInfo.disposalEndTime = '';
    }
  };

  // 获取当前位置
  const getCurrentLocation = () => {
    emit('get-current-location');
  };

  // 服务类型变化处理
  const handleServiceTypeChange = (value: number) => {
    // 触发父组件的服务类型变化事件
    emit('service-type-change', value);
  };

  // 委托单选择过滤
  const filterOption = (input: string, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 处理附件列表回显的函数
  const processAttachmentList = (attachmentList: any[]) => {
    // 分离图片和附件
    const images: any[] = [];
    const attachments: any[] = [];

    attachmentList.forEach((item) => {
      const fileData = {
        fileName: item.fileName,
        filePath: item.filePath,
        fileSize: item.fileSize,
        fileType: item.fileType,
      };

      // 根据 fileType 判断是图片还是附件
      if (item.fileType === 'image') {
        images.push(fileData);
      } else {
        attachments.push(fileData);
      }
    });

    // 更新表单数据 - JUpload 组件期望接收 JSON 字符串格式
    if (images.length > 0) {
      const imagesJson = JSON.stringify(images);
      formData.value.materials.images = imagesJson;
      formData.value.other.images = imagesJson;
      console.log('设置图片数据:', imagesJson);
    }

    if (attachments.length > 0) {
      const attachmentsJson = JSON.stringify(attachments);
      formData.value.materials.attachments = attachmentsJson;
      formData.value.other.attachments = attachmentsJson;
      console.log('设置附件数据:', attachmentsJson);
    }

    console.log('回显处理完成:', {
      images: images.length,
      attachments: attachments.length,
    });
  };

  // 监听 hgyAttachmentList 变化，处理附件回显
  watch(
    () => formData.value.hgyAttachmentList,
    (newAttachmentList) => {
      if (newAttachmentList && Array.isArray(newAttachmentList) && newAttachmentList.length > 0) {
        console.log('Step1组件接收到附件列表，开始处理回显:', newAttachmentList);
        processAttachmentList(newAttachmentList);
      }
    },
    { immediate: true, deep: true }
  );

  // 表单验证规则
  const rules = computed(() => {
    const baseRules = {
      serviceType: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
      entrustOrderId: [{ required: true, message: '请选择关联委托单号', trigger: 'change' }],
      location: {
        province: [{ required: true, message: '请选择省份', trigger: 'change' }],
        city: [{ required: true, message: '请选择城市', trigger: 'change' }],
        area: [{ required: true, message: '请选择区域', trigger: 'change' }],
        detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
      },
    };

    // 采购信息特有验证规则
    if (formData.value.serviceType === 3) {
      baseRules['entrustInfo'] = {
        noticeName: [{ required: true, message: '请输入公告名称', trigger: 'blur' }],
      };
    }

    // 资产处置特有验证规则
    if (formData.value.serviceType === 2) {
      baseRules['basicInfo'] = {
        assetName: [{ required: true, message: '请输入资产名称', trigger: 'blur' }],
        assetType: [{ required: true, message: '请选择资产类型', trigger: 'change' }],
        quantity: [{ required: true, message: '请输入资产数量', trigger: 'blur' }],
        unit: [{ required: true, message: '请选择计量单位', trigger: 'change' }],
        quantityFlag: [{ required: true, message: '请选择是否展示实际数量', trigger: 'change' }],
        serviceLife: [{ required: true, message: '请输入使用年限', trigger: 'blur' }],
        depreciationDegree: [{ required: true, message: '请选择新旧程度', trigger: 'change' }],
        currentStatus: [{ required: true, message: '请选择当前状态', trigger: 'change' }],
        appraisalValue: [{ required: true, message: '请输入评估价值', trigger: 'blur' }],
        disposalPrice: [{ required: true, message: '请输入处置底价', trigger: 'blur' }],
        disposalStartTime: [{ required: true, message: '请选择处置开始时间', trigger: 'change' }],
        disposalEndTime: [{ required: true, message: '请选择处置结束时间', trigger: 'change' }],
        paymentMethod: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
        isTaxIncluded: [{ required: true, message: '请选择是否含税', trigger: 'change' }],
      };
    }

    return baseRules;
  });

  // 表单引用
  const formRef = ref();

  // 验证表单
  const validateForm = async (): Promise<boolean> => {
    try {
      // 添加延迟确保DOM已经渲染完成
      await new Promise((resolve) => setTimeout(resolve, 100));
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }

      return false;
    }
  };

  // 暴露验证方法
  defineExpose({
    validateForm,
  });
</script>

<style lang="less" scoped>
  .step1 {
    .form-section {
      margin-bottom: 32px;
    }

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #262626;
      margin-bottom: 16px;
      padding-bottom: 8px;
      display: flex;
      align-items: center;
      &::before {
        content: '';
        display: block;
        width: 4px;
        height: 18px;
        margin-right: 8px;
        background-color: #004c66;
      }
    }

    .form-row {
      &:last-child {
        border-bottom: none;
      }
    }

    // 服务类型选择样式
    .service-type-item {
      margin-bottom: 0;

      .service-type-select {
        width: 100%;
      }
      /* 服务类型选择框文字颜色 */
      :deep(.ant-select-selection-item) {
        color: #004c66 !important;
      }

      .edit-mode-tip {
        margin-top: 4px;

        .tip-text {
          font-size: 12px;
          color: #999;
          line-height: 1.4;
        }
      }
    }

    // 委托单号选择样式
    .entrust-order-item {
      margin-bottom: 0;

      .entrust-order-select {
        width: 100%;
      }
      /* 委托单号选择框文字颜色 */
      :deep(.ant-select-selection-item) {
        color: #004c66 !important;
      }
    }

    .entrust-placeholder {
      flex: 1; /* 占位元素占一份，保持三等分 */
    }

    // 上传组件样式
    .upload-container {
      display: flex;
      align-items: flex-end;
      gap: 12px;
    }

    .upload-tip {
      font-size: 14px;
      color: #999;
      line-height: 1.4;
      align-self: flex-end;
      flex: 1;
    }

    .upload-item {
      width: 100%;
      margin-bottom: 0;

      .upload-container {
        .upload-tip {
          margin-top: 8px;
          color: #999;
          font-size: 12px;
          line-height: 1.4;
        }
      }
    }

    .upload-normal {
      cursor: pointer;
      flex-shrink: 0;

      :deep(.ant-upload-select) {
        width: 100px !important;
        height: 100px !important;
        background-color: #f2f2f2 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;

        &::before,
        &::after {
          display: none !important;
        }

        .ant-upload {
          width: 100% !important;
          height: 100% !important;
          background-color: #f2f2f2 !important;
          border: none !important;
          border-radius: 4px !important;
          position: relative !important;
          overflow: hidden !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &::before,
          &::after {
            display: none !important;
          }

          .anticon,
          span,
          .ant-upload-text,
          .ant-upload-hint,
          * {
            display: none !important;
          }

          &::after {
            content: '+' !important;
            width: 22px !important;
            height: 21px !important;
            font-size: 18px !important;
            color: #ddd !important;
            font-weight: 300 !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: transparent !important;
            border: none !important;
            line-height: 1 !important;
          }
        }

        &:hover {
          background-color: #e8e8e8 !important;
          border-color: #bbb !important;

          .ant-upload {
            background-color: #e8e8e8 !important;

            &::after {
              color: #004c66 !important;
            }
          }
        }
      }
    }

    .upload-entrust {
      flex-shrink: 0;

      :deep(.ant-upload-select) {
        width: 178px !important;
        height: 100px !important;
        background-color: #f2f2f2 !important;
        border: 1px solid #ddd !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;

        &::before,
        &::after {
          display: none !important;
        }

        .ant-upload {
          width: 100% !important;
          height: 100% !important;
          background-color: #f2f2f2 !important;
          border: none !important;
          border-radius: 4px !important;
          position: relative !important;
          overflow: hidden !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;

          &::before,
          &::after {
            display: none !important;
          }

          .anticon,
          span,
          .ant-upload-text,
          .ant-upload-hint,
          * {
            display: none !important;
          }

          &::after {
            content: '+' !important;
            width: 22px !important;
            height: 21px !important;
            font-size: 18px !important;
            color: #ddd !important;
            font-weight: 300 !important;
            position: absolute !important;
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: transparent !important;
            border: none !important;
            line-height: 1 !important;
          }
        }

        &:hover {
          background-color: #e8e8e8 !important;
          border-color: #bbb !important;

          .ant-upload {
            background-color: #e8e8e8 !important;

            &::after {
              color: #004c66 !important;
            }
          }
        }
      }
    }

    // 基础表单行布局
    .basic-three-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .basic-three-item {
      flex: 1;
      min-width: 200px;
    }

    .basic-two-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .basic-two-item {
      flex: 1;
      min-width: 200px;
    }

    .entrust-two-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .entrust-two-item {
      flex: 1;
      min-width: 200px;
    }

    .full-width-item {
      width: 100%;
      margin-bottom: 0;
    }

    // 服务类型行布局
    .service-type-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
      justify-content: start;
    }

    .entrust-order-item {
      flex: 1;
      min-width: 200px;
    }

    .service-type-item {
      flex: 1;
      min-width: 200px;
    }

    // 资产信息行布局
    .asset-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .asset-item {
      flex: 1;
      min-width: 200px;
    }

    // 资产详情行布局
    .asset-detail-row {
      display: flex;
      gap: 20px;
      margin-bottom: 16px;
    }

    .asset-detail-item {
      flex: 1;
      min-width: 200px;
    }

    // 位置选择样式
    .location-row {
      display: flex;
      gap: 24px;
      align-items: flex-start;

      .location-selects {
        flex: 1;
        min-width: 0;

        .location-area-item {
          margin-bottom: 0;

          :deep(.area-select) {
            .ant-select {
              // 设置选择框文字居中
              .ant-select-selector {
                display: flex;
                align-items: center;

                .ant-select-selection-item {
                  text-align: center;
                  width: 100%;
                  display: flex;
                  align-items: center;
                }

                .ant-select-selection-placeholder {
                  text-align: center;
                  width: 100%;
                  display: flex;
                  align-items: center;
                }
              }
            }
          }
        }
      }
    }

    // 为发布采购信息情况下的省市区选择框添加居中样式
    .location-selects {
      .location-area-item {
        :deep(.area-select) {
          .ant-select {
            // 设置选择框文字居中
            .ant-select-selector {
              display: flex;
              align-items: center;

              .ant-select-selection-item {
                text-align: center;
                width: 100%;
                display: flex;
                align-items: center;
              }

              .ant-select-selection-placeholder {
                text-align: center;
                width: 100%;
                display: flex;
                align-items: center;
              }
            }
          }
        }
      }
    }

    .detail-address {
      flex: 1;
      min-width: 0;

      .detail-address-item {
        margin-bottom: 0;

        .location-btn {
          color: #1890ff;
          border: none;
          background: none;
          padding: 0;
          height: auto;
          line-height: 1;
        }
      }
    }

    // 表单项样式调整
    :deep(.ant-form-item) {
      margin-bottom: 16px;
      align-items: flex-start;

      .ant-form-item-label {
        text-align: left;
        width: auto;
        min-width: 90px;
        padding-right: 0;
        display: flex;
        align-items: center;
        justify-content: end;
        height: 40px;

        label {
          color: #666;
          font-size: 16px;
          font-weight: 400;
          line-height: 1;

          &::after {
            content: '';
            margin: 0;
          }
        }
      }

      .ant-form-item-control {
        flex: 1;
        margin-left: 10px;
      }

      .ant-form-item-control-input {
        min-height: 40px;
      }

      .ant-select .ant-select-selector,
      .ant-picker {
        height: 40px !important;
        line-height: 40px !important;
      }

      .ant-input-number-input {
        height: 38px !important;
      }
    }

    // 输入框样式
    :deep(.ant-input),
    :deep(.ant-select-selector),
    :deep(.ant-picker),
    :deep(.ant-input-number),
    :deep(.ant-textarea) {
      border-radius: 6px;
    }

    // JUpload 组件样式调整
    :deep(.upload-normal) {
      .ant-upload-select-picture-card {
        width: 104px;
        height: 104px;
        margin-right: 8px;
        margin-bottom: 8px;
      }
    }

    :deep(.upload-entrust) {
      .ant-upload-list {
        .ant-upload-list-item {
          margin-bottom: 8px;
        }
      }
    }

    // 编辑模式提示样式
    .edit-mode-tip {
      margin-top: 4px;

      .tip-text {
        font-size: 12px;
        color: #999;
        line-height: 1.4;
      }
    }
  }
</style>
