# 排名组件动画效果说明

## 概述

为 `TargetRanking.vue` 和 `AssetRanking.vue` 两个排名组件添加了动画效果，实现页面首次加载时数据进度条从零开始滚动到目标值的效果。

## 动画特性

### 1. 进度条动画
- **效果**: 进度条宽度从 0% 逐渐增长到目标百分比
- **时长**: 2秒
- **缓动**: 使用 `easeOutQuart` 缓动函数，先快后慢
- **延迟**: 每个项目依次启动，间隔 200ms

### 2. 数字动画
- **效果**: 数值从 0 逐渐增长到目标值
- **时长**: 2秒，与进度条同步
- **精度**: 保留一位小数
- **缓动**: 与进度条使用相同的缓动函数

### 3. 启动时机
- **延迟**: 组件挂载后延迟 500ms 开始动画
- **顺序**: 按排名顺序依次启动，营造瀑布流效果

## 技术实现

### 动画控制状态
```typescript
// 控制进度条是否开始动画
const animatedPremiumAmountData = ref<boolean[]>(new Array(dataLength).fill(false));
const animatedPremiumRateData = ref<boolean[]>(new Array(dataLength).fill(false));

// 存储动画过程中的数值
const animatedPremiumAmountValues = ref<number[]>(new Array(dataLength).fill(0));
const animatedPremiumRateValues = ref<number[]>(new Array(dataLength).fill(0));
```

### 缓动函数
```typescript
// easeOutQuart 缓动函数，实现先快后慢的效果
const easeOutQuart = 1 - Math.pow(1 - progress, 4);
```

### CSS 过渡
```css
.progress-fill {
  transition: width 2s ease;
  transition-delay: ${index * 200}ms; // 动态延迟
}
```

## 动画流程

1. **组件挂载** → `onMounted` 触发
2. **延迟 500ms** → 确保组件完全渲染
3. **依次启动** → 每个项目间隔 200ms 开始动画
4. **同步执行** → 进度条和数字动画同时进行
5. **2秒完成** → 所有动画在 2秒内完成

## 使用示例

### 基本使用
```vue
<template>
  <TargetRanking />
  <AssetRanking />
</template>
```

### 测试动画
访问测试页面：`/hgy/test-animation`

可以通过"重新加载动画"按钮重复观看动画效果。

## 性能优化

1. **requestAnimationFrame**: 使用浏览器原生动画API，确保流畅性
2. **数组预分配**: 提前创建固定长度的数组，避免动态扩容
3. **条件渲染**: 通过布尔值控制动画启动，避免不必要的计算
4. **缓动函数**: 使用数学函数而非CSS动画，减少重排重绘

## 自定义配置

可以通过修改以下参数来调整动画效果：

```typescript
// 动画总时长（毫秒）
const ANIMATION_DURATION = 2000;

// 项目间启动间隔（毫秒）
const ITEM_DELAY = 200;

// 整体启动延迟（毫秒）
const START_DELAY = 500;
```

## 浏览器兼容性

- **现代浏览器**: 完全支持
- **IE11+**: 支持（需要 polyfill）
- **移动端**: 完全支持

## 注意事项

1. 动画只在组件首次挂载时触发
2. 如需重复播放，需要重新挂载组件
3. 数据变化不会触发动画，只有初始加载会有动画效果
4. 建议在数据加载完成后再显示组件，以确保动画效果正常
