<template>
  <div class="step-panel">
    <!-- 使用 Ant Design 表单组件包裹整个表单 -->
    <a-form
      ref="formRef"
      layout="horizontal"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 4, style: { textAlign: 'left' } }"
      :wrapper-col="{ span: 20 }"
      :scroll-to-first-error="true"
    >
      <!-- 信息有效性 -->
      <div class="form-section">
        <h3 class="section-title">信息有效性</h3>
        <div class="form-row">
          <a-form-item label="时间信息" :name="['validity', 'validDate']" required class="form-item-third">
            <a-date-picker
              v-model:value="formData.validity.validDate"
              placeholder="请选择有效期时间"
              style="width: 100%"
              size="large"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
            />
          </a-form-item>
          <a-form-item class="form-item-third"> </a-form-item>
          <a-form-item class="form-item-third"> </a-form-item>
        </div>
      </div>

      <!-- 联系人信息 -->
      <div class="form-section">
        <h3 class="section-title">联系人信息</h3>
        <div class="form-row">
          <a-form-item label="联系姓名" :name="['contactInfo', 'contactName']" required class="form-item-third">
            <a-input v-model:value="formData.contactInfo.contactName" placeholder="请输入联系姓名" size="large" />
          </a-form-item>
          <a-form-item label="联系电话" :name="['contactInfo', 'contactPhone']" required class="form-item-third">
            <a-input v-model:value="formData.contactInfo.contactPhone" placeholder="请输入联系电话" maxlength="11" size="large" />
          </a-form-item>
          <a-form-item class="form-item-third"> </a-form-item>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref } from 'vue';
  import type { FormInstance } from 'ant-design-vue';
  import dayjs from 'dayjs';

  // 定义 Props
  interface Props {
    modelValue: {
      validity: {
        validDate: string;
      };
      contactInfo: {
        contactName: string;
        contactPhone: string;
      };
    };
  }

  const props = defineProps<Props>();

  // 定义 Emits
  interface Emits {
    (e: 'update:modelValue', value: Props['modelValue']): void;
  }

  const emit = defineEmits<Emits>();

  // 表单引用
  const formRef = ref<FormInstance>();

  // 计算属性：表单数据
  const formData = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  // 表单校验规则
  const formRules = {
    validity: {
      validDate: [{ required: true, message: '请选择有效期时间', trigger: 'change' }],
    },
    contactInfo: {
      contactName: [
        { required: true, message: '请输入联系姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '联系姓名长度应在2-20个字符之间', trigger: 'blur' },
      ],
      contactPhone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' },
      ],
    },
  };

  // 禁用过去的日期
  const disabledDate = (current: any) => {
    // 不能选择今天之前的日期
    return current && current < dayjs().startOf('day');
  };

  // 表单验证方法
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return false;
    }
  };

  // 清除表单验证
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  // 暴露方法给父组件
  defineExpose({
    validateForm,
    clearValidate,
  });
</script>

<style lang="less" scoped>
  .step-panel {
    background: #fff;
    border-radius: 8px;
  }

  .form-section {
    margin-bottom: 32px;
  }

  .section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    padding-bottom: 8px;
    &::before {
      content: '';
      display: block;
      width: 4px;
      height: 18px;
      margin-right: 8px;
      background-color: #004c66;
    }
  }

  /* 表单行布局 */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    gap: 20px;
  }

  /* 时间信息占三分之一 */
  .form-item-third {
    flex: 1;
    min-width: calc(33.333% - 14px);
  }

  /* 确保label和输入框之间有10px间距 */
  :deep(.ant-form-item-label) {
    padding-right: 0 !important;
  }

  :deep(.ant-form-item-label > label) {
    margin-right: 0;
  }

  /* 统一设置所有表单项的label宽度和间距 */
  :deep(.ant-form-item) {
    margin-bottom: 16px;
    align-items: flex-start; /* 改为顶部对齐，避免校验错误影响 */

    .ant-form-item-label {
      text-align: left;
      width: auto;
      min-width: 90px;
      padding-right: 0;
      display: flex;
      align-items: center; /* label内容上下居中 */
      height: 40px; /* 固定高度，与输入框高度一致 */

      label {
        color: #666; /* label颜色改为#666 */
        font-size: 16px; /* label字体大小16px */
        font-weight: 400; /* 调整字重 */
        line-height: 1;

        &::after {
          content: '';
          margin: 0;
        }
      }
    }

    .ant-form-item-control {
      flex: 1;
      margin-left: 10px; /* label和输入框间距10px */
    }

    /* 确保输入框容器高度固定 */
    .ant-form-item-control-input {
      min-height: 40px;
    }

    /* 确保所有输入框、选择框、数字输入框保持正确大小 */
    .ant-select .ant-select-selector,
    .ant-picker {
      height: 40px !important;
    }

    .ant-input-number-input {
      height: 38px !important;
    }
  }

  /* 响应式设计 */
  @media (max-width: 768px) {
    .form-item-third {
      min-width: 100%;
    }
  }
</style>
