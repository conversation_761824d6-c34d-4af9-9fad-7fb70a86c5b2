# 数据大屏全屏功能说明

## 功能概述

为数据大屏页面添加了F11全屏功能，当用户按下F11键时，数据大屏会占满整个屏幕，隐藏所有侧边栏、顶部状态栏、标签栏等界面元素。

## 使用方法

### 进入全屏
- **F11键**: 按下F11键进入全屏模式
- **程序控制**: 调用 `enterFullscreen()` 函数

### 退出全屏
- **F11键**: 再次按下F11键退出全屏
- **ESC键**: 按下ESC键退出全屏
- **程序控制**: 调用 `exitFullscreen()` 函数

## 技术实现

### 1. 键盘事件监听
```typescript
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape' && isFullscreen.value) {
    exitFullscreen();
  } else if (event.key === 'F11') {
    event.preventDefault(); // 阻止浏览器默认的F11行为
    if (isFullscreen.value) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }
};
```

### 2. 全屏状态控制
```typescript
const toggleVisualFullscreen = (isFullscreen: boolean) => {
  const visualContainer = document.querySelector('.visual-container') as HTMLElement;
  
  if (isFullscreen) {
    // 进入全屏：让数据大屏容器覆盖整个屏幕
    if (visualContainer) {
      visualContainer.classList.add('fullscreen');
    }
  } else {
    // 退出全屏：恢复正常布局
    if (visualContainer) {
      visualContainer.classList.remove('fullscreen');
    }
  }
};
```

### 3. CSS样式实现
```less
.visual-container {
  &.fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    margin: 0 !important;
    padding: 0 !important;
    min-height: 100vh !important;
    
    // 确保完全覆盖屏幕，不受任何父容器影响
    transform: none !important;
    max-width: none !important;
    max-height: none !important;
  }
}
```

## 功能特点

### ✅ 完全全屏
- 数据大屏容器使用 `position: fixed` 脱离文档流
- 直接覆盖整个视口，不受父容器限制
- 使用 `z-index: 9999` 确保在最顶层

### ✅ 无缝切换
- F11键可以在全屏和窗口模式间无缝切换
- 保持数据大屏的响应式适配
- 退出全屏后恢复原有布局

### ✅ 多种退出方式
- F11键切换
- ESC键退出
- 浏览器全屏API退出

## 浏览器兼容性

- **现代浏览器**: 完全支持
- **Chrome/Edge**: 完全支持
- **Firefox**: 完全支持
- **Safari**: 完全支持

## 注意事项

1. **阻止默认行为**: F11键的默认浏览器全屏行为被阻止，改为控制数据大屏全屏
2. **样式优先级**: 使用 `!important` 确保全屏样式不被其他CSS覆盖
3. **响应式适配**: 全屏时仍然保持数据大屏的响应式缩放功能
4. **状态同步**: 全屏状态与浏览器原生全屏API保持同步

## 与原有功能的区别

### 之前的实现问题
- 只是隐藏侧边栏和顶部栏，但留有空白区域
- 数据大屏容器仍受父容器布局限制

### 现在的实现优势
- 数据大屏容器直接覆盖整个屏幕
- 没有任何空白区域
- 真正的全屏显示效果

## 使用示例

```typescript
// 进入全屏
enterFullscreen();

// 退出全屏
exitFullscreen();

// 检查全屏状态
if (isFullscreen.value) {
  console.log('当前处于全屏模式');
}
```
