<template>
  <div class="p-4">
    <BasicTable @register="registerTable" @button-tag-change="handleButtonTagChange">
      <!-- 操作栏 -->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :size="'small'" />
      </template>

      <!-- 委托类型列 -->
      <template #entrustType="{ text }">
        <a-tag :color="getEntrustTypeColor(text)">
          {{ getEntrustTypeText(text) }}
        </a-tag>
      </template>

      <!-- 服务类型列 -->
      <template #serviceType="{ text }">
        <a-tag :color="getServiceTypeColor(text)">
          {{ getServiceTypeText(text) }}
        </a-tag>
      </template>

      <!-- 审核状态列 -->
      <template #status="{ text }">
        <a-tag :color="getStatusColor(text)">
          {{ getStatusText(text) }}
        </a-tag>
      </template>
    </BasicTable>

    <!-- 审核弹窗 -->
    <CommonAuditModal v-model:open="auditModalVisible" :record="currentAuditRecord" @close="handleCloseAuditModal" @success="handleAuditComplete" />
  </div>
</template>

<script lang="ts" setup name="Backlog">
  import { ref } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { ActionItem, BasicColumn } from '/@/components/Table';
  import { formatToDateTime } from '/@/utils/dateUtil';
  import { queryPageAll, BacklogRecord } from '/@/api/manageCenter/backlog';
  import { CommonAuditModal } from '/@/components/Audit';

  // 表格列定义
  const columns: BasicColumn[] = [
    {
      title: '序号',
      dataIndex: 'index',
      width: 60,
      customRender: ({ index }) => index + 1,
    },
    {
      title: '委托单号',
      dataIndex: 'id',
      width: 150,
    },
    {
      title: '项目名称',
      dataIndex: 'projectName',
      width: 200,
      ellipsis: true,
    },
    {
      title: '委托类型',
      dataIndex: 'entrustType',
      width: 100,
      slots: { customRender: 'entrustType' },
    },
    {
      title: '服务类型',
      dataIndex: 'serviceType',
      width: 120,
      slots: { customRender: 'serviceType' },
    },
    {
      title: '审核状态',
      dataIndex: 'status',
      width: 100,
      slots: { customRender: 'status' },
    },
    {
      title: '联系人',
      dataIndex: 'relationUser',
      width: 100,
    },
    {
      title: '联系电话',
      dataIndex: 'relationPhone',
      width: 120,
    },
    {
      title: '申请人',
      dataIndex: 'applicantUser',
      width: 100,
    },
    {
      title: '审核人',
      dataIndex: 'auditUser',
      width: 100,
    },
    {
      title: '提交时间',
      dataIndex: 'submitTime',
      width: 150,
      customRender: ({ text }) => {
        return text ? formatToDateTime(text) : '-';
      },
    },
    {
      title: '审核时间',
      dataIndex: 'auditTime',
      width: 150,
      customRender: ({ text }) => {
        return text ? formatToDateTime(text) : '-';
      },
    },
  ];

  // 按钮样式标签行配置 - 按委托类型分类
  const buttonTagItems = [
    { key: 'all', label: '全部待办', icon: '' },
    { key: 'appreciation', label: '增值委托', icon: '' },
    { key: 'autonomous', label: '自主委托', icon: '' },
    { key: 'supply', label: '供求信息', icon: '' },
  ];

  const activeButtonTagKey = ref<string | number>('all');

  // 存储当前导航的查询参数
  const currentNavParams = ref<any>({});

  // 处理按钮标签切换
  function handleButtonTagChange(key: string | number) {
    activeButtonTagKey.value = key;

    // 根据导航key设置不同的查询参数 - 按委托类型过滤
    let searchParams = {};
    switch (key) {
      case 'appreciation':
        searchParams = {
          entrustType: 1, // 增值委托
        };
        break;
      case 'autonomous':
        searchParams = {
          entrustType: 2, // 自主委托
        };
        break;
      case 'supply':
        searchParams = {
          entrustType: 3, // 供求信息
        };
        break;
      default:
        searchParams = {}; // 全部待办，不设置过滤条件
    }

    // 存储导航参数
    currentNavParams.value = searchParams;
    // 重新加载数据
    reload();
  }

  // 自定义API调用函数，可以在这里添加额外的参数处理逻辑
  async function customQueryPageAll(params: any) {
    // 合并导航参数和搜索表单参数
    const mergedParams = {
      ...params,
      ...currentNavParams.value,
    };
    return queryPageAll(mergedParams);
  }

  // 表格配置
  const [registerTable, { reload, getForm }] = useTable({
    api: customQueryPageAll, // 使用自定义的API函数
    columns,
    striped: false,
    useSearchForm: true,
    showTableSetting: false,
    bordered: false,
    showIndexColumn: false,
    canResize: true,
    // 按钮样式标签行配置
    showButtonTags: true,
    buttonTagItems,
    activeButtonTagKey: activeButtonTagKey.value,
    inset: true,
    maxHeight: 512,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
      slots: { customRender: 'action' },
      fixed: 'right',
    },
    formConfig: {
      labelWidth: 64,
      // 设置表单整体大小，可选值："default" | "small" | "large" | undefined"
      size: 'large',
      // 设置label对齐方式：'left' | 'right'
      labelAlign: 'left',
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      schemas: [
        {
          field: 'entrustOrderId',
          label: '委托单号',
          component: 'Input',
          componentProps: {
            placeholder: '请输入委托单号',
          },
          colProps: { span: 6 },
        },
        {
          field: 'entrustType',
          label: '委托类型',
          component: 'Select',
          componentProps: {
            placeholder: '请选择委托类型',
            options: [
              { label: '增值', value: 1 },
              { label: '自主', value: 2 },
              { label: '供应', value: 3 },
            ],
          },
          colProps: { span: 6 },
        },
        {
          field: 'serviceType',
          label: '服务类型',
          component: 'Select',
          componentProps: {
            placeholder: '请选择服务类型',
            options: [
              { label: '竞价委托', value: 1 },
              { label: '资产处置', value: 2 },
              { label: '采购信息', value: 3 },
              { label: '供应', value: 4 },
              { label: '求购', value: 5 },
            ],
          },
          colProps: { span: 6 },
        },
      ],
    },
  });

  // 操作按钮配置
  function getTableAction(record: BacklogRecord): ActionItem[] {
    return [
      {
        label: '审核',
        onClick: handleAudit.bind(null, record),
        ifShow: () => canAudit(record),
      },
    ];
  }

  // 权限判断函数
  function canAudit(record: BacklogRecord) {
    // 只有待审核的记录可以审核
    return record.status === 2;
  }

  // 审核弹窗状态
  const auditModalVisible = ref(false);
  const currentAuditRecord = ref<BacklogRecord | null>(null);

  // 事件处理函数
  function handleAudit(record: BacklogRecord) {
    currentAuditRecord.value = record;
    auditModalVisible.value = true;
    console.log('审核记录:', record);
  }

  // 关闭审核弹窗
  function handleCloseAuditModal() {
    auditModalVisible.value = false;
    currentAuditRecord.value = null;
  }

  // 审核完成后的回调
  function handleAuditComplete() {
    handleCloseAuditModal();
    // 刷新列表
    reload();
  }

  // 委托类型处理函数
  function getEntrustTypeText(type: number) {
    const typeMap: Record<number, string> = {
      1: '增值',
      2: '自主',
      3: '供求',
    };
    return typeMap[type] || '未知';
  }

  function getEntrustTypeColor(type: number) {
    const colorMap: Record<number, string> = {
      1: 'blue',
      2: 'green',
      3: 'orange',
    };
    return colorMap[type] || 'default';
  }

  // 服务类型处理函数
  function getServiceTypeText(type: number) {
    const typeMap: Record<number, string> = {
      1: '竞价委托',
      2: '资产处置',
      3: '采购信息',
      4: '供应',
      5: '求购',
    };
    return typeMap[type] || '未知';
  }

  function getServiceTypeColor(type: number) {
    const colorMap: Record<number, string> = {
      1: 'purple',
      2: 'cyan',
      3: 'geekblue',
      4: 'lime',
      5: 'magenta',
    };
    return colorMap[type] || 'default';
  }

  // 审核状态处理函数
  function getStatusText(status: number) {
    const statusMap: Record<number, string> = {
      2: '待审核',
      3: '已通过',
      4: '已拒绝',
    };
    return statusMap[status] || '未知';
  }

  function getStatusColor(status: number) {
    const colorMap: Record<number, string> = {
      2: 'processing',
      3: 'success',
      4: 'error',
    };
    return colorMap[status] || 'default';
  }
</script>

<style lang="less" scoped>
  .p-4 {
    padding: 0;
    :deep(.ant-pagination) {
      margin-bottom: -24px !important;
    }
  }
</style>
