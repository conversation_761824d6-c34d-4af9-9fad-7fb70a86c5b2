<template>
  <div class="authentication-container">
    <!-- 顶部导航切换 -->
    <div class="auth-nav">
      <div class="nav-tab" :class="{ active: activeTab === 'enterprise' }" @click="switchTab('enterprise')"> 企业认证 </div>
      <div class="nav-tab" :class="{ active: activeTab === 'personal' }" @click="switchTab('personal')"> 个人认证 </div>
    </div>

    <!-- 内容区域 -->
    <div class="auth-content">
      <!-- 企业认证表单 -->
      <div v-if="activeTab === 'enterprise'" class="form-section">
        <!-- 企业名称 -->
        <div class="form-item">
          <label class="form-label">企业名称</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.enterpriseName" placeholder="请输入企业名称" class="form-input" />
          </div>
        </div>

        <!-- 统一社会信用代码 -->
        <div class="form-item">
          <label class="form-label">统一社会信用代码</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.creditCode" placeholder="请输入统一社会信用代码" class="form-input" />
          </div>
        </div>

        <!-- 业务联系人 -->
        <div class="form-item">
          <label class="form-label">业务联系人</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.relationUser" placeholder="请输入业务联系人" class="form-input" />
          </div>
        </div>

        <!-- 联系电话 -->
        <div class="form-item">
          <label class="form-label">联系电话</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.relationPhone" placeholder="请输入联系电话" class="form-input" />
          </div>
        </div>

        <!-- 法人真实姓名 -->
        <div class="form-item">
          <label class="form-label">法人真实姓名</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.legalName" placeholder="请输入法人真实姓名" class="form-input" />
          </div>
        </div>

        <!-- 法人证件类型 -->
        <div class="form-item">
          <label class="form-label">法人证件类型</label>
          <div class="form-value">
            <a-radio-group v-model:value="enterpriseForm.cartType">
              <a-radio :value="1">身份证</a-radio>
              <a-radio :value="2">其他</a-radio>
            </a-radio-group>
          </div>
        </div>

        <!-- 法人证件号 -->
        <div class="form-item">
          <label class="form-label">法人证件号</label>
          <div class="form-value">
            <a-input v-model:value="enterpriseForm.cartId" placeholder="请输入法人证件号" class="form-input" />
          </div>
        </div>

        <!-- 营业执照照片 -->
        <div class="form-item">
          <label class="form-label">营业执照照片</label>
          <div class="form-value">
            <JUpload v-model:value="enterpriseForm.companyLogo" :maxCount="1" file-type="image" bizPath="temp" text="上传营业执照" />
          </div>
        </div>

        <!-- 身份证正反面照片 -->
        <div class="form-item">
          <label class="form-label">身份证正反面照片</label>
          <div class="form-value">
            <JUpload v-model:value="enterpriseAttachments" :maxCount="2" file-type="image" bizPath="temp" text="上传身份证照片" />
          </div>
        </div>

        <!-- 企业简介 -->
        <div class="form-item">
          <label class="form-label">企业简介</label>
          <div class="form-value">
            <a-textarea v-model:value="enterpriseForm.description" placeholder="请输入企业简介" :rows="4" class="form-input" />
          </div>
        </div>
      </div>

      <!-- 个人认证表单 -->
      <div v-if="activeTab === 'personal'" class="form-section">
        <!-- 真实姓名 -->
        <div class="form-item">
          <label class="form-label">真实姓名</label>
          <div class="form-value">
            <a-input v-model:value="personalForm.name" placeholder="请输入真实姓名" class="form-input" />
          </div>
        </div>

        <!-- 手机号 -->
        <div class="form-item">
          <label class="form-label">手机号</label>
          <div class="form-value">
            <a-input v-model:value="personalForm.phone" placeholder="请输入手机号" class="form-input" />
          </div>
        </div>

        <!-- 证件类型 -->
        <div class="form-item">
          <label class="form-label">证件类型</label>
          <div class="form-value">
            <a-radio-group v-model:value="personalForm.cartType">
              <a-radio :value="1">身份证</a-radio>
              <a-radio :value="2">其他</a-radio>
            </a-radio-group>
          </div>
        </div>

        <!-- 证件号 -->
        <div class="form-item">
          <label class="form-label">证件号</label>
          <div class="form-value">
            <a-input v-model:value="personalForm.cartId" placeholder="请输入证件号" class="form-input" />
          </div>
        </div>

        <!-- 身份证正反面照片 -->
        <div class="form-item">
          <label class="form-label">身份证正反面照片</label>
          <div class="form-value">
            <JUpload v-model:value="personalAttachments" :maxCount="2" file-type="image" bizPath="temp" text="上传身份证照片" />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <a-button class="save-btn" @click="handleSave" :loading="saveLoading">保存</a-button>
      <a-button type="primary" class="submit-btn" @click="handleSubmit" :loading="submitLoading">提交审核</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { JUpload } from '/@/components/Form/src/jeecg/components/JUpload';
  import { useUserStore } from '/@/store/modules/user';
  import {
    PersonalAuthData,
    EnterpriseAuthData,
    AttachmentItem,
    submitPersonalAuth,
    submitEnterpriseAuth,
    getPersonalAuth,
    getEnterpriseAuth,
  } from '/@/api/system/userAuth';
  import { getTenantId } from '/@/utils/auth';

  // 当前激活的标签页
  const activeTab = ref<'enterprise' | 'personal'>('enterprise');
  const saveLoading = ref(false);
  const submitLoading = ref(false);

  const userStore = useUserStore();

  // 企业认证表单数据
  const enterpriseForm = reactive<Partial<EnterpriseAuthData>>({
    enterpriseName: '',
    creditCode: '',
    relationUser: '',
    relationPhone: '',
    legalName: '',
    cartType: 1,
    cartId: '',
    companyLogo: '',
    description: '',
    review: 1,
  });

  // 个人认证表单数据
  const personalForm = reactive<Partial<PersonalAuthData>>({
    name: '',
    phone: '',
    cartType: 1,
    cartId: '',
    review: 1,
  });

  // 企业认证附件
  const enterpriseAttachments = ref<string>('');
  // 个人认证附件
  const personalAttachments = ref<string>('');

  // 获取租户ID和用户ID
  const tenantId = getTenantId();
  const userId = computed(() => userStore.getUserInfo?.id);

  /**
   * 切换标签页
   */
  const switchTab = (tab: 'enterprise' | 'personal') => {
    activeTab.value = tab;
    loadAuthData();
  };

  /**
   * 加载认证数据
   */
  const loadAuthData = async () => {
    console.log('加载认证数据', userStore.getUserInfo);
    if (!userId.value) {
      console.warn('用户ID不存在，无法加载认证数据');
      return;
    }

    try {
      if (activeTab.value === 'enterprise') {
        const response = await getEnterpriseAuth(userId.value);
        console.log('企业认证数据:', response);

        if (response) {
          // 处理企业认证数据结构
          if (response.hgyEnterpriseAuth) {
            // 如果返回的是包装结构，提取 hgyEnterpriseAuth 数据
            const authData = response.hgyEnterpriseAuth;
            Object.assign(enterpriseForm, {
              enterpriseName: authData.enterpriseName,
              creditCode: authData.creditCode,
              relationUser: authData.relationUser,
              relationPhone: authData.relationPhone,
              legalName: authData.legalName,
              cartType: authData.cartType,
              cartId: authData.cartId,
              companyLogo: authData.companyLogo,
              description: authData.description,
              review: authData.review,
            });
          } else {
            // 如果返回的是直接的认证数据
            Object.assign(enterpriseForm, response);
          }

          // 处理附件数据
          const attachmentList = response.hgyAttachmentList;
          if (attachmentList && attachmentList.length > 0) {
            enterpriseAttachments.value = attachmentList.map((item) => item.filePath).join(',');
          }
        }
      } else {
        const response = await getPersonalAuth(userId.value);
        console.log('个人认证数据:', response);

        if (response) {
          // 处理个人认证数据结构
          if (response.hgyPersonalAuth) {
            // 如果返回的是包装结构，提取个人认证数据
            const authData = response.hgyPersonalAuth;
            Object.assign(personalForm, {
              name: authData.name,
              phone: authData.phone,
              cartType: authData.cartType,
              cartId: authData.cartId,
              review: authData.review,
            });
          } else {
            // 如果返回的是直接的认证数据
            Object.assign(personalForm, response);
          }

          // 处理附件数据
          const attachmentList = response.hgyAttachmentList;
          if (attachmentList && attachmentList.length > 0) {
            personalAttachments.value = attachmentList.map((item) => item.filePath).join(',');
          }
        }
      }
    } catch (error) {
      console.error('加载认证数据失败:', error);
    }
  };

  /**
   * 处理附件数据
   */
  const processAttachments = (attachmentStr: string): AttachmentItem[] => {
    if (!attachmentStr) return [];

    const paths = attachmentStr.split(',').filter((path) => path.trim());
    return paths.map((path, index) => ({
      bizType: 'image',
      fileName: `证件照片${index + 1}`,
      filePath: path.trim(),
      fileSize: '0',
      fileType: 'image',
    }));
  };

  /**
   * 处理保存操作
   */
  const handleSave = async () => {
    if (!userId.value) {
      message.warning('用户信息异常，请重新登录');
      return;
    }

    saveLoading.value = true;
    try {
      if (activeTab.value === 'enterprise') {
        const submitData: EnterpriseAuthData = {
          ...(enterpriseForm as EnterpriseAuthData),
          userId: String(userId.value),
          attachmentList: processAttachments(enterpriseAttachments.value),
        };
        await submitEnterpriseAuth(submitData);
      } else {
        const submitData: PersonalAuthData = {
          ...(personalForm as PersonalAuthData),
          userId: String(userId.value),
          attachmentList: processAttachments(personalAttachments.value),
        };
        await submitPersonalAuth(submitData);
      }
      message.success('保存成功');
    } catch (error) {
      message.error('保存失败');
      console.error('保存失败:', error);
    } finally {
      saveLoading.value = false;
    }
  };

  /**
   * 处理提交审核操作
   */
  const handleSubmit = async () => {
    if (!userId.value) {
      message.warning('用户信息异常，请重新登录');
      return;
    }

    // 表单验证
    if (activeTab.value === 'enterprise') {
      if (!enterpriseForm.enterpriseName || !enterpriseForm.creditCode || !enterpriseForm.legalName) {
        message.warning('请填写完整的企业信息');
        return;
      }
    } else {
      if (!personalForm.name || !personalForm.phone || !personalForm.cartId) {
        message.warning('请填写完整的个人信息');
        return;
      }
    }

    submitLoading.value = true;
    try {
      if (activeTab.value === 'enterprise') {
        const submitData: EnterpriseAuthData = {
          ...(enterpriseForm as EnterpriseAuthData),
          userId: String(userId.value),
          attachmentList: processAttachments(enterpriseAttachments.value),
        };
        await submitEnterpriseAuth(submitData);
      } else {
        const submitData: PersonalAuthData = {
          ...(personalForm as PersonalAuthData),
          userId: String(userId.value),
          attachmentList: processAttachments(personalAttachments.value),
        };
        await submitPersonalAuth(submitData);
      }
      message.success('提交审核成功');
    } catch (error) {
      message.error('提交失败');
      console.error('提交失败:', error);
    } finally {
      submitLoading.value = false;
    }
  };

  onMounted(() => {
    loadAuthData();
  });
</script>

<style scoped lang="less">
  .authentication-container {
    .auth-nav {
      display: flex;
      height: 55px;
      width: 100%;
      border-radius: 10px;

      .nav-tab {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: #eee;
        color: #999;

        &.active {
          background-color: #fff;
          color: #004c66;
        }

        &:hover:not(.active) {
          background-color: #e0e0e0;
        }

        &:first-child {
          border-top-left-radius: 10px;
        }

        &:last-child {
          border-top-right-radius: 10px;
        }
      }
    }

    .auth-content {
      padding: 20px;
      min-height: 680px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        bottom: 20px;
        width: calc(100% - 40px);
        height: 1px;
        background: linear-gradient(to right, rgba(221, 221, 221, 1), rgba(255, 255, 255, 1));
      }

      .form-section {
        .form-item {
          padding: 20px 0;
          border-bottom: 1px solid;
          border-image: linear-gradient(to right, rgba(221, 221, 221, 1), rgba(255, 255, 255, 1));
          border-image-slice: 1;

          .form-label {
            font-size: 16px;
            color: #333;
            flex-shrink: 0;
            font-family: 'PingFang Bold';
            margin-bottom: 10px;
            display: block;
          }

          .form-value {
            flex: 1;

            .form-input {
              width: 100%;
              border: none;
              outline: none;
              font-size: 16px;
              color: #666;
              background: transparent;
              transition: border-color 0.3s ease;

              &::placeholder {
                color: #999;
                font-size: 16px;
              }

              &:focus {
                border-bottom-color: #004c66;
              }
            }

            :deep(.ant-input) {
              border: none;
              outline: none;
              font-size: 16px;
              color: #666;
              background: transparent;
              box-shadow: none;
              padding: 0;

              &::placeholder {
                color: #999;
                font-size: 16px;
              }

              &:focus {
                border-bottom-color: #004c66;
                box-shadow: none;
              }
            }

            :deep(.ant-input-affix-wrapper) {
              border: none;
              background: transparent;
              box-shadow: none;
              padding: 0;

              &:focus,
              &:focus-within {
                border-bottom-color: #004c66;
                box-shadow: none;
              }
            }

            :deep(.ant-radio-group) {
              .ant-radio-wrapper {
                color: #666;
                font-size: 16px;
              }
            }

            :deep(.ant-upload-wrapper) {
              .ant-upload-list {
                margin-top: 10px;
              }
            }
          }
        }
      }
    }

    .action-buttons {
      display: flex;
      gap: 16px;
      padding: 0 20px 20px 20px;

      .save-btn,
      .submit-btn {
        border-radius: 6px;
        font-size: 16px;
        min-width: 120px;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .save-btn {
        background-color: rgba(0, 76, 102, 0.2);
        color: #004c66;
        border: 1px solid rgba(0, 76, 102, 0.3);

        &:hover {
          background-color: rgba(0, 76, 102, 0.3);
          border-color: rgba(0, 76, 102, 0.4);
        }
      }

      .submit-btn {
        background-color: #004c66;
        border-color: #004c66;

        &:hover {
          background-color: #003a4d;
          border-color: #003a4d;
        }
      }
    }
  }
</style>
