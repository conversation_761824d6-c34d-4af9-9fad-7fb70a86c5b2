# VisualHeader 组件功能说明

## 新增功能

### 1. 省市区级联选择器

- **功能**: 点击"选择省市区"按钮，直接显示省市区级联下拉框
- **数据源**: 使用JeecgBoot内置的省市区数据
- **组件**: 使用 `a-cascader` 组件
- **显示**: 选择后按钮文本会显示选中的省市区名称，格式为"省 / 市 / 区"
- **交互**: 点击按钮直接展开下拉选择，无需弹窗

### 2. 物资种类级联选择器

- **功能**: 点击"请选择物资种类"按钮，直接显示物资种类级联下拉框
- **数据源**: 通过接口 `/hgy/material/hgyMaterialType/getMaterialTree` 获取
- **组件**: 使用 `a-cascader` 组件
- **搜索**: 支持搜索功能，可以按物资名称搜索
- **显示**: 选择后按钮文本会显示选中的物资种类路径
- **交互**: 点击按钮直接展开下拉选择，无需弹窗

### 3. 返回灰谷云按钮

- **状态**: 暂时隐藏
- **说明**: 按照需求暂时隐藏该按钮

## 事件说明

### materialChange 事件

当用户选择物资种类并确认时触发

```typescript
// 事件参数
{
  value: string[], // 选中的物资ID数组
  text: string     // 显示的物资名称路径
}
```

### areaChange 事件

当用户选择省市区并确认时触发

```typescript
// 事件参数
{
  value: string[], // 选中的省市区代码数组
  text: string     // 显示的省市区名称路径
}
```

## 使用示例

```vue
<template>
  <VisualHeader @material-change="handleMaterialChange" @area-change="handleAreaChange" />
</template>

<script setup>
  const handleMaterialChange = (value, text) => {
    console.log('物资类型变化:', { value, text });
    // 根据选择的物资类型更新数据
  };

  const handleAreaChange = (value, text) => {
    console.log('省市区变化:', { value, text });
    // 根据选择的省市区更新数据
  };
</script>
```

## 数据格式

### 物资类型数据格式

```typescript
interface MaterialTypeNode {
  id: string;
  parentId: string;
  name: string;
  code: string;
  level: number;
  leaf: number;
  sort: number;
  status: number;
  delFlag: number;
  children?: MaterialTypeNode[];
}
```

### 省市区数据格式

使用JeecgBoot标准的省市区数据格式，每个选项包含：

- `value`: 地区代码
- `label`: 地区名称

## 样式特性

- 下拉框采用深色主题，与数据大屏风格保持一致
- 按钮保持原有的科技感发光效果和切角设计
- 级联选择器完全隐藏，按钮外观不变
- 下拉选项使用半透明背景和青色边框
- 支持悬停和选中状态的视觉反馈
- 搜索框采用统一的深色主题样式
